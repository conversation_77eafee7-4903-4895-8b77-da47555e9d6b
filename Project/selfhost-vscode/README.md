# Self-hosted VSCode với Docker Compose

Dự án này cung cấp một giải pháp hoàn chỉnh để self-host VSCode trên Ubuntu sử dụng Docker Compose và code-server.

## Tính năng

- ✅ VSCode chạy trong browser với đầy đủ tính năng
- ✅ Hỗ trợ extensions từ Open VSX Registry
- ✅ Hỗ trợ GitHub Copilot và các AI coding assistants
- ✅ Persistent data và cấu hình
- ✅ Cấu hình linh hoạt qua environment variables
- ✅ Healthcheck và auto-restart
- ✅ Security và permission handling

## Yêu cầu hệ thống

- Ubuntu 18.04+ hoặc bất kỳ Linux distribution nào hỗ trợ Docker
- Docker Engine 20.10+
- Docker Compose 2.0+
- Ít nhất 2GB RAM
- Ít nhất 5GB disk space

## Cài đặt nhanh

### 1. Clone repository

```bash
git clone <repository-url>
cd selfhost-vscode
```

### 2. Cấu hình environment variables

Chỉnh sửa file `.env` theo nhu cầu:

```bash
cp .env.example .env
nano .env
```

<PERSON><PERSON><PERSON> biến quan trọng:
- `HOST_IP`: IP address mà server sẽ bind (0.0.0.0 cho tất cả interfaces)
- `HOST_PORT`: Port mà code-server sẽ listen (mặc định: 8080)
- `CODE_SERVER_PASSWORD`: Password để truy cập (để trống để dùng random password)
- `PUID/PGID`: User/Group ID để tránh permission issues

### 3. Tạo thư mục data

```bash
mkdir -p data/{config,local,workspace,ssh}
```

### 4. Khởi chạy services

```bash
docker-compose up -d
```

### 5. Truy cập VSCode

Mở browser và truy cập: `http://YOUR_SERVER_IP:8080`

Nếu bạn không set password, check logs để lấy random password:

```bash
docker-compose logs code-server
```

## Cấu hình nâng cao

### Cài đặt Extensions

Bạn có thể cài extensions theo 2 cách:

#### 1. Qua Web UI
- Truy cập VSCode trong browser
- Vào Extensions tab (Ctrl+Shift+X)
- Search và install extensions như bình thường

#### 2. Qua Command Line
```bash
# Vào container
docker-compose exec code-server bash

# Cài extension
code-server --install-extension ms-python.python
code-server --install-extension GitHub.copilot
```

### GitHub Copilot Setup

1. Cài đặt GitHub Copilot extension
2. Đăng nhập GitHub account có Copilot subscription
3. Authorize extension trong VSCode

### SSL/HTTPS Setup

Để setup HTTPS, bạn có thể:

1. **Sử dụng reverse proxy (Nginx/Caddy)**
2. **Sử dụng Cloudflare Tunnel**
3. **Cấu hình Let's Encrypt**

Ví dụ với Nginx:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection upgrade;
        proxy_set_header Accept-Encoding gzip;
    }
}
```

## Quản lý

### Xem logs
```bash
docker-compose logs -f code-server
```

### Restart service
```bash
docker-compose restart code-server
```

### Update code-server
```bash
docker-compose pull
docker-compose up -d
```

### Backup data
```bash
tar -czf vscode-backup-$(date +%Y%m%d).tar.gz data/
```

### Restore data
```bash
tar -xzf vscode-backup-YYYYMMDD.tar.gz
```

## Troubleshooting

### Permission Issues
Nếu gặp permission issues, check và update PUID/PGID trong `.env`:

```bash
id $USER  # Lấy UID và GID của user hiện tại
```

### Extensions không load
- Check internet connection
- Verify EXTENSIONS_GALLERY URLs trong `.env`
- Restart container

### Performance Issues
- Tăng RAM allocation cho Docker
- Check disk space
- Monitor container resources: `docker stats`

## Security Notes

- Đổi default password
- Sử dụng HTTPS trong production
- Cấu hình firewall properly
- Regular backup data
- Keep Docker images updated

## Support

Nếu gặp vấn đề, check:
1. Docker logs: `docker-compose logs`
2. Container status: `docker-compose ps`
3. System resources: `docker stats`
4. Network connectivity: `curl http://localhost:8080`
