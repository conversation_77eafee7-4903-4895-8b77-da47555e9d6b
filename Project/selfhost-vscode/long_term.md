- project này được tạo ra để tạo ra 1 docker compose sử dụng cho mục đích self host vscode trên hệ điều hành ubuntu
- địa chỉ ip của máy chủ sẽ được lấy từ biến môi trường `HOST_IP`
- đìa chỉ port của máy chủ sẽ được lấy từ biến môi trường `HOST_PORT`
- các biến môi trường sẽ được lấy từ file `.env` trong thư mục gốc của project
- các biến môi trường sẽ được sử dụng để cấu hình các dịch vụ trong docker compose
- các dịch vụ sẽ được cấu hình để sử dụng địa chỉ ip và port của máy chủ
- mục đích cuối cùng là 1 docker compose có thể chạy được trên máy chủ và có thể truy cập từ xa thông qua vscode
- vscode sau khi self host sẽ có thể sử dụng các extension và cài đặt của người dùng
- vs code sau khi self host có thể sử dụng được github copilot