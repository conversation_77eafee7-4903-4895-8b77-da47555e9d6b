# Sequential Thinking Tool - Hướng dẫn sử dụng

## Tổng quan

Sequential Thinking là một tool đặc biệt cho phép AI assistant thực hiện quá trình suy nghĩ có cấu trúc, linh hoạt và có thể điều chỉnh trong quá trình giải quyết vấn đề phức tạp.

## Mục đích chính

- **Phân tích vấn đề phức tạp** thành các bước nhỏ hơn
- **Lập kế hoạch động** có thể điều chỉnh trong quá trình thực hiện
- **Xử lý uncertainty** và thay đổi hướng khi cần
- **Du<PERSON> trì context** qua nhiều bước suy nghĩ
- **Lọc thông tin** không liên quan
- **Tạo và verify hypothesis** trước khi đưa ra kết luận

## Khi nào sử dụng Sequential Thinking?

### Điều kiện trigger:

1. **V<PERSON>n đề phức tạp đa bước**
   - <PERSON><PERSON><PERSON> phân tích nhiều khía cạnh
   - Yêu cầu lập kế hoạch chi tiết
   - Có nhiều dependencies giữa các bước

2. **Thiếu thông tin ban đầu**
   - Cần thu thập thêm thông tin trong quá trình
   - Phạm vi vấn đề chưa rõ ràng
   - Có thể cần thay đổi approach

3. **Cần maintain context**
   - Vấn đề có nhiều components liên quan
   - Cần theo dõi progress qua nhiều bước
   - Có thể cần backtrack hoặc revise

4. **Uncertainty cao**
   - Không chắc chắn về approach tốt nhất
   - Cần explore multiple paths
   - Có thể cần pivot strategy

## Cách sử dụng

### Parameters chính:

- **thought**: Nội dung suy nghĩ hiện tại
- **nextThoughtNeeded**: Có cần thought tiếp theo không
- **thoughtNumber**: Số thứ tự thought hiện tại
- **totalThoughts**: Ước tính tổng số thoughts cần thiết
- **isRevision**: Có phải đang revise thought trước đó không
- **revisesThought**: Số thứ tự thought đang được revise
- **branchFromThought**: Điểm branch từ thought nào
- **branchId**: ID của branch hiện tại

### Workflow pattern:

1. **Khởi tạo** với ước tính số thoughts
2. **Phân tích** từng bước một cách tuần tự
3. **Điều chỉnh** totalThoughts nếu cần
4. **Revise** thoughts trước đó nếu phát hiện sai sót
5. **Branch** sang hướng khác nếu cần
6. **Kết thúc** khi đạt được solution thỏa mãn

## Ví dụ thực tế từ conversation này

### Context:
Người dùng yêu cầu tạo docker-compose cho self-host VSCode

### Sequential thinking process:

```
Thought 1: Phân tích yêu cầu
- Hiểu requirements: docker-compose, VSCode, Ubuntu, extensions
- Cần đọc long_term.md để hiểu context
- Cần research về code-server solutions

Thought 2: Research và planning
- Đọc được context từ long_term.md
- Tìm hiểu code-server là solution tốt nhất
- Cần tìm documentation về docker setup

Thought 3: Implementation planning
- Có đủ thông tin từ Context7 documentation
- Plan: tạo .env, docker-compose.yml, README.md
- Cần setup git repository trước

Thought 4: Execution
- Setup git và branch
- Tạo workflow file
- Bắt đầu implement các files

Thought 5: Completion
- Hoàn thành tất cả files
- Validate setup với docker-compose config
- Update workflow progress
```

## Best Practices

### 1. Ước tính thoughts realistically
- Bắt đầu với số conservative
- Sẵn sàng tăng nếu cần
- Không ngại điều chỉnh trong quá trình

### 2. Sử dụng revision khi cần
- Khi phát hiện sai sót trong thoughts trước
- Khi có thông tin mới thay đổi approach
- Khi cần clarify hoặc expand ý tưởng

### 3. Branch cho alternative approaches
- Khi có nhiều cách giải quyết
- Khi cần explore different options
- Khi stuck và cần thử hướng khác

### 4. Maintain clear thought structure
- Mỗi thought nên có mục đích rõ ràng
- Avoid redundant thoughts
- Keep thoughts focused và actionable

## Lợi ích

1. **Structured problem solving**: Tránh được chaos thinking
2. **Transparency**: User có thể theo dõi reasoning process
3. **Flexibility**: Có thể adjust approach mid-way
4. **Quality control**: Có cơ hội review và revise
5. **Learning**: Có thể analyze thinking patterns sau này

## Khi KHÔNG nên sử dụng

- **Simple, straightforward tasks**: Overhead không cần thiết
- **Well-defined problems**: Khi approach đã rõ ràng
- **Time-sensitive requests**: Khi cần response nhanh
- **Factual queries**: Khi chỉ cần lookup information

## Kết luận

Sequential Thinking là một powerful tool cho complex problem solving, nhưng cần sử dụng đúng context và timing. Nó đặc biệt hữu ích cho các tasks như:

- Software architecture planning
- Complex troubleshooting
- Multi-step implementations
- Research và analysis tasks
- Creative problem solving

Key là biết khi nào sử dụng và khi nào không, để tối ưu hóa efficiency và quality của output.
