version: '3.8'

services:
  code-server:
    image: codercom/code-server:latest
    container_name: vscode-server
    restart: unless-stopped
    
    # Environment variables
    environment:
      - TZ=${TZ}
      - PUID=${PUID}
      - PGID=${PGID}
      - PASSWORD=${CODE_SERVER_PASSWORD}
      - SUDO_PASSWORD=${CODE_SERVER_PASSWORD}
      - DEFAULT_WORKSPACE=/home/<USER>/${WORKSPACE_DIR}
      - EXTENSIONS_GALLERY={"serviceUrl":"${EXTENSIONS_GALLERY_SERVICE_URL}","itemUrl":"${EXTENSIONS_GALLERY_ITEM_URL}"}
    
    # Port mapping từ environment variables
    ports:
      - "${HOST_IP}:${HOST_PORT}:8080"
    
    # Volume mounts để persist data
    volumes:
      # Config directory để lưu cấu hình code-server
      - ./data/config:/home/<USER>/.config
      # Local data directory để lưu extensions và settings
      - ./data/local:/home/<USER>/.local
      # Workspace directory cho projects
      - ./data/workspace:/home/<USER>/${WORKSPACE_DIR}
      # SSH keys (nếu cần)
      - ./data/ssh:/home/<USER>/.ssh
      # Git config
      - ./data/gitconfig:/home/<USER>/.gitconfig
    
    # User mapping để tránh permission issues
    user: "${PUID}:${PGID}"
    
    # Security options
    security_opt:
      - seccomp:unconfined
    
    # Healthcheck
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Labels for better organization
    labels:
      - "com.docker.compose.project=selfhost-vscode"
      - "com.docker.compose.service=code-server"

# Networks (optional - sử dụng default network)
networks:
  default:
    name: vscode-network
